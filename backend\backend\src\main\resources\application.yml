server:
  port: 8080

spring:
  application:
    name: user-auth-system

  main:
    allow-bean-definition-overriding: true

  # 數據庫配置
  datasource:
    url: *********************************************************************************************************************************************************
    username: root
    password: NDI1694T5q0WDy3p11bhtz4lo489W29D3Xd
    driver-class-name: com.mysql.cj.jdbc.Driver
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        format_sql: true
    open-in-view: false
    generate-ddl: true
  
  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password:
      database: 0
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms
  
  # 郵件配置
  mail:
    # Gmail 配置示例
    host: smtp.gmail.com
    port: 587
    username: ni<PERSON><PERSON>@gmail.com
    password: pddi avwc nxuw eqam

    # QQ郵箱配置示例（取消註釋使用）
    # host: smtp.qq.com
    # port: 587
    # username: <EMAIL>
    # password: your-qq-auth-code

    # 163郵箱配置示例（取消註釋使用）
    # host: smtp.163.com
    # port: 25
    # username: <EMAIL>
    # password: your-163-auth-code

    # Outlook/Hotmail 配置示例（取消註釋使用）
    # host: smtp-mail.outlook.com
    # port: 587
    # username: <EMAIL>
    # password: your-outlook-password

    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
          timeout: 5000
          connectiontimeout: 5000
          writetimeout: 5000
          # 如果使用SSL，取消下面註釋
          # ssl:
          #   enable: true
          # socketFactory:
          #   class: javax.net.ssl.SSLSocketFactory
          #   port: 465
  
  # 文件上傳配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

# JWT配置
jwt:
  secret: mySecretKey123456789012345678901234567890
  access-token-expiration: 7200000  # 2小時 (2 * 60 * 60 * 1000)
  refresh-token-expiration: 604800000  # 7天 (7 * 24 * 60 * 60 * 1000)
  # 保留舊配置以兼容現有代碼
  expiration: 7200000 # 2小時

# 自定義配置
app:
  email:
    verification:
      expiration: 300000 # 5分鐘
      max-attempts-per-5min: 2
      max-attempts-per-day: 5
  
  file:
    upload-dir: ./uploads/
    max-size: 10MB

# 支付寶配置
alipay:
  config:
    appid: 9021000129631387  # 商戶ID
    serverUrl: https://openapi-sandbox.dl.alipaydev.com/gateway.do # 支付寶網關地址
    alipay-public-key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnvhJ5idfRRgEB1mZysTpeSWQsdas1pJtAKhss2rY2kHhtN1pP7ha2lZQhFYK2ve6Welq/6N74JXs4wS6e2+3ILyHU0lcG6szpO6KynrxEeDbul4YnhIpA3czHD+9B+/i7qN5Onha3f43RlqDnQN0vOfhy1lgEjXtgzrvKa8jnd7Uu87mNFLUM9BahDAPhSS6T0cDtOj6jlWYdgMqdl/w6yfbF5OjIlzfV/JpnS6E7ZgLxHsjR7ssvg6vzuhJpu0QivyrxmLzXVOIeSlkCTRi/igItdcBffGlvgCvai4hbidWDRfRyqX9KYnWXkow4JeKzY0sxLwQUntxnJWmUAjLCwIDAQAB
    private-key: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCj7uGMzsgwO1ebVDe3X0+Q6kAaoT/qkv63WlrQWOz73H2KQ3GKNyoDug0hA3poIjGEw+gJmTz7VN3LKmSsz0p/wBDI6KOkV07QusTzkVtoFqvNh2gOKP+dEQNzZVxrLoIhf087tVP1toH/E4Fe1isZ4iAidpA+BvOR4v69d0LEv4dgeWaKES+EFKqxainu1vNpQsl7ac8E9ZS+uGet1Huetd3gHacen5uLfjjWmp61PG67f0bdh8oJNJDFTfVbeM+wxjo7vQxZSnn5LuNqM9vOFZgCYVoQ9JKDVUSGBMMFwFbuWKZhntsBjRrPR54BlaSS98tWc05JqZFFKfe9Af8BAgMBAAECggEANQ3onUOG8mUiMzkMVyJXg9TsLjIll2s/WGO7pFZDTDfNwWOWaV71EAO0oAyaHv2B/S/R9tlLLSQIK+pqbqsftzyXsg3oeBVxgogPmaxfYs6Dh57dBEgsgc6xyTTythV1NBYZtH84zvrkE4NiiSjTBityrvSkNg7V4U8Plo30Y+v76boXcbd1/jHjRSuLwIk/odO7VOYXQ2w2hiDJAeydJR01VEbA3WU+yHcNcjkSaiYEJPL2aeiZMhtq1LuXcb2nIMUOrxDV6pdRZBXCIeURgLrW/YH/XraxU2R7j2CYRA/vaR+hcmYtV8fZhmrRw7fYotkBNPg6a7lKV5be1DXNgQKBgQDRAkZMDasqcLDzItreT9lxX+nv9EWFkLHux/45TM4kdBthBK85XdG9+OteKW4DsfbkW0rWJ7+T0vt+ZX6/4p+PYbsN3u17gx71LV5NGHcfrzcjNOd9R1QaOJZpJe6vEad01sWcEV7qlhnKzayryX13Ee0BVQDc/bAQcFjsQNFqCQKBgQDIyjldmKGA0bs6t+zTNkLDJuz67e9Y0BLELrDsCPGwMkaMlAaK3RgqOBm/Qz5dUkyiT4LUQEha98Q5mWa7EYShrsE3XmK7mDy00SLBb8z2wgxZU9+HFWZwG44VYmbaa8dYmsKi+L+XnF3u6euR8a8ePU+WDsE3oMGYYYLsmPsLOQKBgQCYicZwPxGK2c/qwqdl1HTnr452V5pdjmqt0DwT6aARLsPEnLyda4Fl2kM855OpErsTkiUeAshoxHRTnRNSS70T0cnIp8g+ekWfvkqyjYZzE1d0VGoWHnac5GuxtcNq9cF3Hj/+VRcmsgGE53J7tYPh4K1OaZFFt8hFVxku42dysQKBgCFUqGcoEg8vQZRCudZp2HVKveX9n1Cv4Z2dXpMf/PbRsbJeVCOzBfLkMynzwKN/KOd8qWwQa7JmFLW3CD2fb9PjDYHiciNP5yvg15MiuvRvS9t8N8vVupZ7jH9yQT6ay5GixxKEllFVjKY/QHKOmxI6/T4cnuhcECb6cOBTmMDJAoGBAIRhbVydzmftOnUrDtrGt5s6gBAWFP6N5SOl27Zq7msD91NO34yRxxwuuUEltX8hyUVfQFG4xx/4tVGhXRWMH/ZRfc+xwCPPVRGG8nV6IydrXuCaiKWOV7AxAhZWJ+i6g9BqgbT6K0IDhjwx1U2iyfT/c6/lhc3qRJJ0Z+tj2hju
    return-url: https://b02ad650a2dc.ngrok-free.app/payment/success #返回頁面
    notify-url: https://b02ad650a2dc.ngrok-free.app/api/payment/alipay/callback #回調地址（使用ngrok公網地址）

# 日誌配置
logging:
  level:
    com.example: DEBUG
    org.springframework.security: DEBUG
